2025-07-25 15:52:41,311 - INFO - Starting middleware for command: /Users/<USER>/Desktop/workspace/AI/mcp/weather/.venv/bin/python3 weather.py
2025-07-25 15:52:41,314 - INFO - REQUEST: {"method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"Cline","version":"3.20.1"}},"jsonrpc":"2.0","id":0}
2025-07-25 15:52:41,315 - INFO - Middleware started and threads are running.
2025-07-25 15:52:41,643 - INFO - RESPONSE: {"jsonrpc":"2.0","id":0,"result":{"protocolVersion":"2025-03-26","capabilities":{"experimental":{},"prompts":{"listChanged":false},"resources":{"subscribe":false,"listChanged":false},"tools":{"listChanged":false}},"serverInfo":{"name":"weather","version":"1.12.1"}}}
2025-07-25 15:52:41,643 - INFO - REQUEST: {"method":"notifications/initialized","jsonrpc":"2.0"}
2025-07-25 15:52:41,643 - INFO - REQUEST: {"method":"tools/list","jsonrpc":"2.0","id":1}
2025-07-25 15:52:41,644 - INFO - RESPONSE: {"jsonrpc":"2.0","id":1,"result":{"tools":[{"name":"get_alerts","description":"Get weather alerts for a US state.\n\n    Args:\n        state: Two-letter US state code (e.g. CA, NY)\n    ","inputSchema":{"properties":{"state":{"title":"State","type":"string"}},"required":["state"],"title":"get_alertsArguments","type":"object"},"outputSchema":{"properties":{"result":{"title":"Result","type":"string"}},"required":["result"],"title":"get_alertsOutput","type":"object"}},{"name":"get_forecast","description":"Get weather forecast for a location.\n\n    Args:\n        latitude: Latitude of the location\n        longitude: Longitude of the location\n    ","inputSchema":{"properties":{"latitude":{"title":"Latitude","type":"number"},"longitude":{"title":"Longitude","type":"number"}},"required":["latitude","longitude"],"title":"get_forecastArguments","type":"object"},"outputSchema":{"properties":{"result":{"title":"Result","type":"string"}},"required":["result"],"title":"get_forecastOutput","type":"object"}}]}}
2025-07-25 15:52:41,644 - INFO - REQUEST: {"method":"resources/list","jsonrpc":"2.0","id":2}
2025-07-25 15:52:41,645 - INFO - RESPONSE: {"jsonrpc":"2.0","id":2,"result":{"resources":[]}}
2025-07-25 15:52:41,645 - INFO - REQUEST: {"method":"resources/templates/list","jsonrpc":"2.0","id":3}
2025-07-25 15:52:41,645 - INFO - RESPONSE: {"jsonrpc":"2.0","id":3,"result":{"resourceTemplates":[]}}
2025-07-28 09:12:54,767 - INFO - Starting middleware for command: /Users/<USER>/Desktop/workspace/AI/mcp/weather/.venv/bin/python3 weather.py
2025-07-28 09:12:54,786 - INFO - REQUEST: {"method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"Cline","version":"3.20.1"}},"jsonrpc":"2.0","id":0}
2025-07-28 09:12:54,786 - INFO - Middleware started and threads are running.
2025-07-28 09:12:56,084 - INFO - RESPONSE: {"jsonrpc":"2.0","id":0,"result":{"protocolVersion":"2025-03-26","capabilities":{"experimental":{},"prompts":{"listChanged":false},"resources":{"subscribe":false,"listChanged":false},"tools":{"listChanged":false}},"serverInfo":{"name":"weather","version":"1.12.1"}}}
2025-07-28 09:12:56,086 - INFO - REQUEST: {"method":"notifications/initialized","jsonrpc":"2.0"}
2025-07-28 09:12:56,086 - INFO - REQUEST: {"method":"tools/list","jsonrpc":"2.0","id":1}
2025-07-28 09:12:56,088 - INFO - RESPONSE: {"jsonrpc":"2.0","id":1,"result":{"tools":[{"name":"get_alerts","description":"Get weather alerts for a US state.\n\n    Args:\n        state: Two-letter US state code (e.g. CA, NY)\n    ","inputSchema":{"properties":{"state":{"title":"State","type":"string"}},"required":["state"],"title":"get_alertsArguments","type":"object"},"outputSchema":{"properties":{"result":{"title":"Result","type":"string"}},"required":["result"],"title":"get_alertsOutput","type":"object"}},{"name":"get_forecast","description":"Get weather forecast for a location.\n\n    Args:\n        latitude: Latitude of the location\n        longitude: Longitude of the location\n    ","inputSchema":{"properties":{"latitude":{"title":"Latitude","type":"number"},"longitude":{"title":"Longitude","type":"number"}},"required":["latitude","longitude"],"title":"get_forecastArguments","type":"object"},"outputSchema":{"properties":{"result":{"title":"Result","type":"string"}},"required":["result"],"title":"get_forecastOutput","type":"object"}}]}}
2025-07-28 09:12:56,091 - INFO - REQUEST: {"method":"resources/list","jsonrpc":"2.0","id":2}
2025-07-28 09:12:56,092 - INFO - RESPONSE: {"jsonrpc":"2.0","id":2,"result":{"resources":[]}}
2025-07-28 09:12:56,096 - INFO - REQUEST: {"method":"resources/templates/list","jsonrpc":"2.0","id":3}
2025-07-28 09:12:56,097 - INFO - RESPONSE: {"jsonrpc":"2.0","id":3,"result":{"resourceTemplates":[]}}
2025-07-28 09:31:01,342 - INFO - Starting middleware for command: /Users/<USER>/Desktop/workspace/AI/mcp/weather/.venv/bin/python3 weather.py
2025-07-28 09:31:01,348 - INFO - REQUEST: {"method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"Cline","version":"3.20.2"}},"jsonrpc":"2.0","id":0}
2025-07-28 09:31:01,348 - INFO - Middleware started and threads are running.
2025-07-28 09:31:01,739 - INFO - RESPONSE: {"jsonrpc":"2.0","id":0,"result":{"protocolVersion":"2025-03-26","capabilities":{"experimental":{},"prompts":{"listChanged":false},"resources":{"subscribe":false,"listChanged":false},"tools":{"listChanged":false}},"serverInfo":{"name":"weather","version":"1.12.1"}}}
2025-07-28 09:31:01,740 - INFO - REQUEST: {"method":"notifications/initialized","jsonrpc":"2.0"}
2025-07-28 09:31:01,741 - INFO - REQUEST: {"method":"tools/list","jsonrpc":"2.0","id":1}
2025-07-28 09:31:01,741 - INFO - RESPONSE: {"jsonrpc":"2.0","id":1,"result":{"tools":[{"name":"get_alerts","description":"Get weather alerts for a US state.\n\n    Args:\n        state: Two-letter US state code (e.g. CA, NY)\n    ","inputSchema":{"properties":{"state":{"title":"State","type":"string"}},"required":["state"],"title":"get_alertsArguments","type":"object"},"outputSchema":{"properties":{"result":{"title":"Result","type":"string"}},"required":["result"],"title":"get_alertsOutput","type":"object"}},{"name":"get_forecast","description":"Get weather forecast for a location.\n\n    Args:\n        latitude: Latitude of the location\n        longitude: Longitude of the location\n    ","inputSchema":{"properties":{"latitude":{"title":"Latitude","type":"number"},"longitude":{"title":"Longitude","type":"number"}},"required":["latitude","longitude"],"title":"get_forecastArguments","type":"object"},"outputSchema":{"properties":{"result":{"title":"Result","type":"string"}},"required":["result"],"title":"get_forecastOutput","type":"object"}}]}}
2025-07-28 09:31:01,742 - INFO - REQUEST: {"method":"resources/list","jsonrpc":"2.0","id":2}
2025-07-28 09:31:01,742 - INFO - RESPONSE: {"jsonrpc":"2.0","id":2,"result":{"resources":[]}}
2025-07-28 09:31:01,743 - INFO - REQUEST: {"method":"resources/templates/list","jsonrpc":"2.0","id":3}
2025-07-28 09:31:01,743 - INFO - RESPONSE: {"jsonrpc":"2.0","id":3,"result":{"resourceTemplates":[]}}
2025-07-30 09:08:26,709 - INFO - Starting middleware for command: /Users/<USER>/Desktop/workspace/AI/mcp/weather/.venv/bin/python3 weather.py
2025-07-30 09:08:26,711 - INFO - REQUEST: {"method":"initialize","params":{"protocolVersion":"2025-03-26","capabilities":{},"clientInfo":{"name":"Cline","version":"3.20.2"}},"jsonrpc":"2.0","id":0}
2025-07-30 09:08:26,711 - INFO - Middleware started and threads are running.
2025-07-30 09:08:27,387 - INFO - RESPONSE: {"jsonrpc":"2.0","id":0,"result":{"protocolVersion":"2025-03-26","capabilities":{"experimental":{},"prompts":{"listChanged":false},"resources":{"subscribe":false,"listChanged":false},"tools":{"listChanged":false}},"serverInfo":{"name":"weather","version":"1.12.1"}}}
2025-07-30 09:08:27,388 - INFO - REQUEST: {"method":"notifications/initialized","jsonrpc":"2.0"}
2025-07-30 09:08:27,388 - INFO - REQUEST: {"method":"tools/list","jsonrpc":"2.0","id":1}
2025-07-30 09:08:27,389 - INFO - RESPONSE: {"jsonrpc":"2.0","id":1,"result":{"tools":[{"name":"get_alerts","description":"Get weather alerts for a US state.\n\n    Args:\n        state: Two-letter US state code (e.g. CA, NY)\n    ","inputSchema":{"properties":{"state":{"title":"State","type":"string"}},"required":["state"],"title":"get_alertsArguments","type":"object"},"outputSchema":{"properties":{"result":{"title":"Result","type":"string"}},"required":["result"],"title":"get_alertsOutput","type":"object"}},{"name":"get_forecast","description":"Get weather forecast for a location.\n\n    Args:\n        latitude: Latitude of the location\n        longitude: Longitude of the location\n    ","inputSchema":{"properties":{"latitude":{"title":"Latitude","type":"number"},"longitude":{"title":"Longitude","type":"number"}},"required":["latitude","longitude"],"title":"get_forecastArguments","type":"object"},"outputSchema":{"properties":{"result":{"title":"Result","type":"string"}},"required":["result"],"title":"get_forecastOutput","type":"object"}}]}}
2025-07-30 09:08:27,390 - INFO - REQUEST: {"method":"resources/list","jsonrpc":"2.0","id":2}
2025-07-30 09:08:27,390 - INFO - RESPONSE: {"jsonrpc":"2.0","id":2,"result":{"resources":[]}}
2025-07-30 09:08:27,391 - INFO - REQUEST: {"method":"resources/templates/list","jsonrpc":"2.0","id":3}
2025-07-30 09:08:27,391 - INFO - RESPONSE: {"jsonrpc":"2.0","id":3,"result":{"resourceTemplates":[]}}
