import subprocess
import sys
import threading
import logging

# --- Configuration ---
LOG_FILE = 'middleware.log'
TARGET_SCRIPT = 'weather.py'
# --- End Configuration ---

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(LOG_FILE),
    ]
)

def forward_and_log(source_stream, dest_stream, stream_name):
    """
    Reads data from a source stream, logs it, and forwards it to a destination stream.
    This function does NOT print to console to keep stdio clean for the parent process.
    """
    try:
        for line in iter(source_stream.readline, b''):
            # Log the message to the file
            log_message = f"{stream_name}: {line.decode('utf-8', errors='ignore').strip()}"
            logging.info(log_message)
            
            # Forward the original message
            dest_stream.write(line)
            dest_stream.flush()
    except Exception as e:
        logging.error(f"Error in {stream_name} forwarder: {e}")
    finally:
        # Avoid closing standard streams (stdin, stdout, stderr)
        if hasattr(source_stream, 'fileno') and source_stream.fileno() > 2:
            source_stream.close()
        if hasattr(dest_stream, 'fileno') and dest_stream.fileno() > 2:
            dest_stream.close()

def main():
    """
    Main function to start the middleware.
    """
    command = [sys.executable, TARGET_SCRIPT]
    logging.info(f"Starting middleware for command: {' '.join(command)}")

    try:
        process = subprocess.Popen(
            command,
            stdin=subprocess.PIPE,
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE, # Capture stderr to log errors from the subprocess
            bufsize=1,
            text=False
        )
    except FileNotFoundError:
        logging.error(f"Error: The script '{TARGET_SCRIPT}' was not found.")
        sys.exit(1)
    except Exception as e:
        logging.error(f"Failed to start subprocess: {e}")
        sys.exit(1)

    # Thread to forward stdin from cline -> subprocess
    request_thread = threading.Thread(
        target=forward_and_log,
        args=(sys.stdin.buffer, process.stdin, 'REQUEST'),
        daemon=True
    )

    # Thread to forward stdout from subprocess -> cline
    response_thread = threading.Thread(
        target=forward_and_log,
        args=(process.stdout, sys.stdout.buffer, 'RESPONSE'),
        daemon=True
    )
    
    # Thread to log stderr from the subprocess, but not forward it to the console
    # We create a dummy sink for it.
    class DevNull:
        def write(self, msg):
            pass
        def flush(self):
            pass

    stderr_thread = threading.Thread(
        target=forward_and_log,
        args=(process.stderr, DevNull(), 'SUBPROCESS_STDERR'),
        daemon=True
    )

    request_thread.start()
    response_thread.start()
    stderr_thread.start()

    logging.info("Middleware started and threads are running.")

    process.wait()

    logging.info(f"Subprocess finished with exit code {process.returncode}.")
    
    request_thread.join(timeout=1)
    response_thread.join(timeout=1)
    stderr_thread.join(timeout=1)
    
    logging.info("Middleware shutting down.")

if __name__ == "__main__":
    main()